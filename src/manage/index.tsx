import React, { useEffect, useState } from 'react';
import { Drawer, Message, Loading, Dialog, Icon } from '@alifd/next';
import { Banner } from '@alife/iec-dtao-delivery-pc';
import { queryAlipayInfo, queryDataIndicatorList, queryMerchantArrears, queryMerchantStatus } from '@/api/query';
import { baseTrail, batchAdmitFixedPrice, fixedOpen, fixedRenewalOpen, giveAdmit, renewalBatchAdmitFixedPrice, renewalBatchTrailFixedPrice, renewalTrailFixedPrice, trailFixedPrice } from '@/api/open';
import { log } from '@ali/iec-dtao-utils';
import { getCookie } from '@/utils/cookie';
import { PROTOCOL_URL, QUIT_TYPE, TRADE_ACCOUNT, PRODUCT_CODE, HOME_BANNER_POSITION_CODE, ByfHomeBtnMarketingTextPC, QIANNIU_PC, RENEWAL_DIALOG_TIPS, renewalDialogStyle, PRICE_RENDER_CONFIG } from '@/constant';
import MenuHeader from '@/components/MenuHeader';
import CommonQa from '@/components/CommonQa';
import FixedFeePanel from './components/FixedFeePanel';
import BaseFeePanel from './components/BaseFeePanel';
import ServiceData from './components/ServiceData';
import NoticePanel from '@/components/NoticePanel';
import DeliveryAnnouncement from '@/components/DeliveryAnnouncement';
import { baseQuit, quitAdmit } from '@/api/quit';
import styles from './index.module.scss';
import useTips from './hooks/useTips';
import { get, includes } from 'lodash-es';
import { queryDelivery } from '@/api/promotion';
import dayjs from 'dayjs';
import { animationDelay } from '@/utils/tools';
import { money_US } from '@ali/iec-dtao-utils/esm/common/number';
import BalloonContent from '@/components/BalloonContent';

const Manage = (props) => {
  const [alipayLoginId, setAlipayLoginId] = useState<string>('');
  const [isShowDetailQA, setIsShowDetailQA] = useState<boolean>(false);
  const [isShowProtocol, setIsShowProtocol] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [partAdmit, setPartAdmit] = useState<boolean>(false);
  const [dataUpdateDeadline, setDataUpdateDeadline] = useState<number | null>(null);
  const [arrearsInfo, setArrearsInfo] = useState<{ isArrears: boolean; showArrears: boolean }>({ isArrears: false, showArrears: false });
  const [renewalDialogVisible, setRenewalDialogVisible] = useState<boolean>(false);

  const [renderContent, setRenderContent] = useState<{
    status: string;
    isOpenFixed?: boolean; // 是否开固定价
    isFixedAdmit?: boolean; // 固定价准入
    arrearsAmount?: string; // 欠费金额
    effectTime: number | null; // 固定价生效时间
    expireTime: number | null; // 固定价失效时间
    baseFeeAmount?: string; // 日常试算金额
    fixedPromotionFlag?: boolean; // 是否命中固定价营销
    fixedFeeAmount?: string; // 固定价试算原价金额
    fixedDiscountFeeAmount?: string; // 固定价试算营销金额
    fixedPriceType: string; // 固定价类型
    discountFeeAmount?: string; // 固定价类型
    promotionFlag?: boolean; // 是否命中正常营销
    promotionHalfOff?: boolean; // 是否命中5折营销
    promotionTitle?: string; // 营销标题
    marketTitle?: string | null; // 用增营销标题
    customerFreezeCode?: string | null; // 冻结码
    giveType: string | null; // 服务设置
    exemptionAmount?: string; // 营销总优惠金额
    promotionDetailList?: any[]; // 营销券
    currFixedPriceExpireSoon?: boolean | null; // 当前一口价是否即将过期
    currFixedPriceRenewal?: boolean | null; // 前一口价是否已续签
    isFixedRenewalAdmit?: boolean; // 框续签准入
    currFixedPriceExpireTime?: string | number | null; // 当前框即将到期时间
  }>({
    status: 'MERCHANT_OPEN_SUCCEED',
    isFixedAdmit: false,
    fixedPriceType: '',
    baseFeeAmount: '',
    fixedFeeAmount: '',
    effectTime: null,
    expireTime: null,
    discountFeeAmount: '',
    fixedDiscountFeeAmount: '',
    fixedPromotionFlag: false,
    promotionFlag: false,
    promotionTitle: '',
    promotionHalfOff: false,
    marketTitle: null,
    customerFreezeCode: null,
    giveType: null,
    exemptionAmount: '',
    promotionDetailList: [],
    currFixedPriceExpireSoon: null,
    currFixedPriceRenewal: null,
    isFixedRenewalAdmit: false,
    currFixedPriceExpireTime: null,
  });

  // 年框续签数据
  const [fixedRenewalData, setFixedRenewalData] = useState<{
    originalFeeAmount: string;
    discountFeeAmount: string;
    promotionFlag: boolean;
    effectTime?: number | null;
    expireTime?: number | null;
    fixedPriceType?: string;
  }>({
    originalFeeAmount: '',
    discountFeeAmount: '',
    promotionFlag: false,
    effectTime: null,
    expireTime: null,
    fixedPriceType: '',
  });

  const [indicatorData, setIndicatorData] = useState<Array<{
    title?: string;
    id?: string;
    value?: string;
    unit?: null | string;
  }>>([]);

  const tips = useTips(
    {
      status: renderContent.status,
      isOpenFixed: renderContent?.isOpenFixed,
      customerFreezeCode: renderContent?.customerFreezeCode,
    },
  );

  const handleOnGetArrearsAmount = async () => {
    try {
      const arrearsData = await queryMerchantArrears();
      const { responseCode, arrears, showArrears } = arrearsData;
      if (responseCode !== 'SUCCESS') {
        log.addLog('query-arrears-amount', 'error', { responseCode });
      }
      setArrearsInfo({ showArrears, isArrears: arrears });
    } catch (error) {
      log.addLog('query-arrears-amount', 'error', { catch: error?.message });
    }
  };

  // 获取营销标题
  const handleOnGetMarketInfo = async () => {
    try {
      const marketRes = await queryDelivery({ positionCode: ByfHomeBtnMarketingTextPC });
      const title = get(marketRes, 'content.items.0.fixedContent') ?? null;
      return title;
    } catch (error) {
      return null;
    }
  };

  // 获取状态
  const handleOnGetStatus = async () => {
    try {
      const statusData = await queryMerchantStatus();
      const {
        status,
        responseCode: statusResponseCode,
        openFixedPrice,
        fixedPriceType: statusFixedPriceType,
        customerFreezeCode: customerFreezeCodeData,
        giveType: giveTypeData,
        currFixedPriceExpireSoon: currFixedPriceExpireSoonData,
        currFixedPriceRenewal: currFixedPriceRenewalData,
        currFixedPriceExpireTime: currFixedPriceExpireTimeData,
      } = statusData;
      if (statusResponseCode !== 'SUCCESS') {
        log.addLog('query-status', 'error', { responseCode: statusResponseCode });
        Message.error('请求失败，请稍后再试！');
        return;
      }
      switch (status) {
        case 'null':
        case 'MERCHANT_OPENING':
        case 'MERCHANT_OPEN_FAILED':
        case 'MERCHANT_QUIT_SUCCEED':
          props?.history.push('/');
          return;
        case 'MERCHANT_FROZEN':
        case 'MERCHANT_QUITTING':
          setRenderContent({
            status,
            effectTime: null,
            expireTime: null,
            isOpenFixed: true,
            customerFreezeCode: customerFreezeCodeData ?? null,
            giveType: giveTypeData,
            fixedPriceType: statusFixedPriceType,
            currFixedPriceExpireSoon: currFixedPriceExpireSoonData,
            currFixedPriceRenewal: currFixedPriceRenewalData,
            currFixedPriceExpireTime: currFixedPriceExpireTimeData,
          });
          return;
      }
      // 未开固定价: 日常试算+固定价准入+固定价试算
      if (!openFixedPrice) {
        // 日常试算
        const baseTrailData = await baseTrail();
        const {
          responseCode: admitAndTrailResponseCode,
          originalFeeAmount: baseFeeAmountData,
          discountFeeAmount: discountFeeAmountData,
          promotionFlag,
          exemptionAmount: exemptionAmountData,
          promotionDetailList,
        } = baseTrailData;
        if (admitAndTrailResponseCode !== 'SUCCESS') {
          log.addLog('query-base-trail', 'error', { responseCode: admitAndTrailResponseCode });
          Message.error('请求失败，请稍后再试！');
        }
        const marketTitle = await handleOnGetMarketInfo();
        // 固定价准入+固定价试算
        const fixedAdmitData = await batchAdmitFixedPrice();
        const {
          responseCode: fixedResponse,
          fixedAdmitAndTrailInfoList,
        } = fixedAdmitData;
        if (fixedResponse !== 'SUCCESS') {
          log.addLog('query-fixed-admit-and-trail', 'error', { responseCode: fixedResponse });
        }
        const {
          isAdmit,
          effectTime,
          expireTime,
          originalFeeAmount: fixedOriginalFeeAmount,
          discountFeeAmount: fixedDiscountFeeAmount,
          promotionFlag: fixedPromotionFlag,
          fixedPriceType,
        } = get(fixedAdmitAndTrailInfoList, '0', {}) || {};
        if (isAdmit) {
          log.addLog('manage-fixed-open-access', 'other');
        }
        setRenderContent({
          status,
          effectTime: effectTime ?? null,
          expireTime: expireTime ?? null,
          fixedPriceType,
          baseFeeAmount: baseFeeAmountData,
          discountFeeAmount: discountFeeAmountData,
          promotionFlag,
          marketTitle,
          giveType: giveTypeData,
          exemptionAmount: exemptionAmountData,
          promotionDetailList,
          isOpenFixed: openFixedPrice,
          isFixedAdmit: isAdmit,
          fixedPromotionFlag,
          fixedFeeAmount: fixedOriginalFeeAmount,
          fixedDiscountFeeAmount,
        });
        return;
      }
      // 已开固定价：固定价试算
      const fixedData = await trailFixedPrice({ fixedPriceType: statusFixedPriceType, openChannel: QIANNIU_PC });
      const {
        responseCode: fixedResponseCode,
        originalFeeAmount: fixedFeeAmountData,
        discountFeeAmount: fixedDiscountFeeAmountData,
        promotionFlag: fixedPromotionFlagData,
        effectTime,
        expireTime,
      } = fixedData;
      if (fixedResponseCode !== 'SUCCESS') {
        log.addLog('query-fixed-trail', 'error', { responseCode: fixedResponseCode });
        Message.error('请求失败，请稍后再试！');
      }
      let isFixedRenewalAdmitData = false;
      // 框即将到期
      if (currFixedPriceExpireSoonData) {
        // 框续约准入
        const fixedRenewalAdmitRes = await renewalBatchAdmitFixedPrice({ openChannel: QIANNIU_PC });
        const {
          responseCode,
          renewalAdmitInfoList,
        } = fixedRenewalAdmitRes;
        if (responseCode !== 'SUCCESS') {
          log.addLog('fixed-renewal-admit', 'error', { responseCode });
        }
        isFixedRenewalAdmitData = renewalAdmitInfoList?.[0]?.isAdmit ?? false;
      }
      // 框已续约
      if (currFixedPriceExpireSoonData && currFixedPriceRenewalData) {
        await handleOnFixedRenewalTrail(statusFixedPriceType);
      }
      setRenderContent({
        status,
        isOpenFixed: openFixedPrice,
        isFixedAdmit: true,
        effectTime: effectTime ?? null,
        expireTime: expireTime ?? null,
        fixedPriceType: statusFixedPriceType,
        fixedFeeAmount: fixedFeeAmountData,
        fixedDiscountFeeAmount: fixedDiscountFeeAmountData,
        fixedPromotionFlag: fixedPromotionFlagData,
        giveType: giveTypeData,
        currFixedPriceExpireSoon: currFixedPriceExpireSoonData,
        currFixedPriceRenewal: currFixedPriceRenewalData,
        isFixedRenewalAdmit: isFixedRenewalAdmitData,
        currFixedPriceExpireTime: currFixedPriceExpireTimeData,
      });
    } catch (error) {
      log.addLog('query-info', 'error', { catch: error?.message });
      Message.error('请求失败，请稍后再试！ ');
    }
  };

  // 获取支付宝信息
  const handleOnGetAlipayInfo = async () => {
    try {
      const alipayInfoData = await queryAlipayInfo({ channel: TRADE_ACCOUNT });
      const { responseCode: alipayInfoResponseCode, alipayLoginId: alipayLoginIdData } = alipayInfoData;
      if (alipayInfoResponseCode !== 'SUCCESS') {
        log.addLog('query-alipay-info', 'error', { responseCode: alipayInfoResponseCode });
        return;
      }
      setAlipayLoginId(alipayLoginIdData);
    } catch (error) {
      log.addLog('query-alipay-info', 'error', { catch: error?.message });
    }
  };

  // 开通固定价
  const handleOnOpenFixedPrice = async () => {
    log.addLog('open-fixed-price-click', 'click');
    setIsLoading(true);
    try {
      const params: any = {
        identifier: `${getCookie()}-${Math.floor(Date.now())}`,
        effectTime: renderContent?.effectTime,
        expireTime: renderContent?.expireTime,
        fixedPriceType: renderContent?.fixedPriceType,
        showFeeAmount: renderContent?.fixedFeeAmount,
        showDiscountFeeAmount: renderContent?.fixedDiscountFeeAmount,
      };
      const fixedPriceData = await fixedOpen(params);
      const { responseCode: fixedPriceResponseCode } = fixedPriceData;
      setIsLoading(false);
      if (fixedPriceResponseCode === 'SUCCESS') {
        log.addLog('open-fixed-price-apply', 'success');
        Message.success(`${PRICE_RENDER_CONFIG[renderContent?.fixedPriceType ?? 'default']?.title}开通成功`);
        window.location.reload();
        return;
      } else if (includes(['DYNAMIC_PARAM_INVALID', 'DYNAMIC_PARAM_INVALID_AMOUNT'], fixedPriceResponseCode)) {
        log.addLog('open-fixed-price-apply', 'error', { responseCode: fixedPriceResponseCode });
        Message.show({
          type: 'loading',
          title: '页面信息已更新，请重新提交开通',
          duration: 2000,
          hasMask: true,
          align: 'tc tc',
          offset: [0, 200],
          afterClose: () => {
            window.location.reload();
          },
        });
        return;
      }
      log.addLog('open-fixed-price-apply', 'error', { responseCode: fixedPriceResponseCode });
      Message.success(`${PRICE_RENDER_CONFIG[renderContent?.fixedPriceType ?? 'default']?.title}开通失败`);
    } catch (error) {
      setIsLoading(false);
      log.addLog('open-fixed-price-apply', 'error', { catch: error?.message });
      Message.success(`${PRICE_RENDER_CONFIG[renderContent?.fixedPriceType ?? 'default']?.title}开通失败`);
    }
  };

  // 退出申请
  const handleOnApplyQuit = async (data, quitReasonDialogRef) => {
    try {
      const reasonData = data ? data.join(',') : null;
      setIsLoading(true);
      // 退出准入
      const adminResult = await quitAdmit({ channel: QIANNIU_PC });
      const { success, isAdmit, attributes } = adminResult;

      if (!success) {
        log.addLog('quit-later-admit-request-fail', 'error', { error: 'SYSTEM_ERROR' });
        Message.error('系统异常,请稍后再试');
        return;
      }

      if (!isAdmit) {
        log.addLog('quit-later-admit-reject-dialog-init', 'visit');
        setIsLoading(false);
        quitReasonDialogRef.current.trigger(false);
        await animationDelay();
        const { name, beginTime, endTime } = get(attributes?.rejectRuleList, '[0]');
        const checkTime = (time, formatRule) => (time && formatRule ? dayjs(time).format(formatRule) : '--');
        Dialog.warning({
          v2: true,
          title: '退出失败',
          content: `您于${checkTime(beginTime, 'YYYY-MM-DD')}参与${name || '--'}活动享受专属优惠，${checkTime(endTime, 'YYYY-MM-DD HH:mm:ss')} 前无法退出，感谢您的理解`,
          onOk: () => {
            log.addLog('quit-later-admit-reject-dialog-ok-click', 'success');
          },
        });
        return;
      }

      log.addLog('quit-reason', 'success', { reason: reasonData });
      // 退出申请
      Message.show({
        type: 'success',
        title: '退出申请提交成功',
        duration: 0,
        hasMask: true,
        align: 'tc tc',
        offset: [0, 200],
      });
      setIsLoading(true);
      const params = {
        identifier: `${getCookie()}-${Math.floor(Date.now())}`,
        scene: QUIT_TYPE,
        reason: reasonData,
      };
      const quitData = await baseQuit(params);
      setIsLoading(false);
      const { responseCode, applicationNo } = quitData;
      if (responseCode !== 'SUCCESS' || !applicationNo) {
        log.addLog('quit-reason-apply', 'error', { responseCode });
        Message.error('退出申请中失败，请稍后重试');
        return;
      }
      log.addLog('quit-reason-apply', 'success');
      Message.show({
        type: 'loading',
        title: '退出申请提交成功，请稍后...',
        duration: 2000,
        hasMask: true,
        align: 'tc tc',
        offset: [0, 200],
        afterClose: () => {
          window.location.reload();
        },
      });
    } catch (error) {
      log.addLog('quit-reason-apply', 'error');
      setIsLoading(false);
    }
  };

  // 赠送准入
  const handleOnGiveAdmit = async () => {
    try {
      const params = {
        openChannel: QIANNIU_PC,
      };
      const giveAdmitRes = await giveAdmit(params);
      const { responseCode, status: giveAdmitStatus } = giveAdmitRes;
      if (responseCode !== 'SUCCESS') {
        log.addLog('query-give-admit', 'error', { responseCode });
        return;
      }
      setPartAdmit(giveAdmitStatus === 'PASS');
    } catch (error) {
      log.addLog('query-give-admit', 'error');
    }
  };

  // 管理指标查询
  const handleOnGetDataIndicatorList = async () => {
    try {
      const dataIndicatorList = await queryDataIndicatorList();
      const { responseCode, indicatorProcessData, endTime } = dataIndicatorList;
      if (responseCode !== 'SUCCESS') {
        log.addLog('query-data-indicator', 'error', { responseCode });
        return;
      }
      log.addLog('query-data-indicator', 'success');
      setIndicatorData(indicatorProcessData);
      setDataUpdateDeadline(endTime);
    } catch (error) {
      log.addLog('query-data-indicator', 'error');
    }
  };

  // 年框续签申请
  const handleOnFixedRenewalApply = async () => {
    try {
      const params: any = {
        identifier: `${getCookie()}-${Math.floor(Date.now())}`,
        effectTime: fixedRenewalData?.effectTime,
        expireTime: fixedRenewalData?.expireTime,
        fixedPriceType: fixedRenewalData?.fixedPriceType,
        showFeeAmount: fixedRenewalData?.originalFeeAmount,
        showDiscountFeeAmount: fixedRenewalData?.discountFeeAmount,
      };
      const fixedRenewalRes = await fixedRenewalOpen(params);
      const { responseCode } = fixedRenewalRes;
      if (responseCode !== 'SUCCESS') {
        log.addLog('fixed-renewal-apply-fail', 'error', { responseCode });
        setRenewalDialogVisible(false);
        Message.error({
          title: '数据更新，将为您刷新页面',
          duration: 2000,
        });
        window.location.reload();
        return;
      }
      log.addLog('fixed-renewal-apply-success', 'success');
      Message.success({
        title: '年框一口价续签成功',
        duration: 2000,
      });
      window.location.reload();
    } catch (error) {
      log.addLog('fixed-renewal-apply-fail', 'error', { error: JSON.stringify(error) });
      setRenewalDialogVisible(false);
      Message.error('数据更新，将为您刷新页面');
    }
  };

  // 框续约试算（待生效）
  const handleOnFixedRenewalTrail = async (type?) => {
    const fixedRenewalTrailRes = await renewalTrailFixedPrice({
      fixedPriceType: type || renderContent?.fixedPriceType,
      openChannel: QIANNIU_PC,
    });
    const { success,
      responseCode,
      originalFeeAmount: originalFeeAmountData,
      discountFeeAmount: discountFeeAmountData,
      promotionFlag: promotionFlagData,
      effectTime: effectTimeData,
      expireTime: expireTimeData,
    } = fixedRenewalTrailRes;
    if (!success || responseCode !== 'SUCCESS' || !originalFeeAmountData) {
      log.addLog('fixed-renewal-trail', 'error', { responseCode });
      Message.error({
        title: '数据更新，将为您刷新页面',
        duration: 2000,
      });
      window.location.reload();
      return;
    }
    setFixedRenewalData({
      originalFeeAmount: originalFeeAmountData,
      discountFeeAmount: discountFeeAmountData,
      promotionFlag: promotionFlagData,
      effectTime: effectTimeData,
      expireTime: expireTimeData,
      fixedPriceType: type || renderContent?.fixedPriceType,
    });
  };

  // 续签试算（未开）
  const handleOnUnOpenFixedRenewlTrail = async () => {
    const unopenFiexedRenewelTrailRes = await renewalBatchTrailFixedPrice({
      openChannel: QIANNIU_PC,
    });
    const {
      responseCode: fixedResponse,
      fixedAdmitAndTrailInfoList,
    } = unopenFiexedRenewelTrailRes;
    if (fixedResponse !== 'SUCCESS') {
      Message.error({
        title: '数据更新，将为您刷新页面',
        duration: 2000,
      });
      window.location.reload();
      return false;
    }
    const {
      originalFeeAmount: fixedOriginalFeeAmount,
      discountFeeAmount: fixedDiscountFeeAmount,
      promotionFlag: fixedPromotionFlag,
      fixedPriceType,
      effectTime: effectTimeData,
      expireTime: expireTimeData,
    } = get(fixedAdmitAndTrailInfoList, '0', {}) || {};
    setFixedRenewalData({
      originalFeeAmount: fixedOriginalFeeAmount,
      discountFeeAmount: fixedDiscountFeeAmount,
      promotionFlag: fixedPromotionFlag,
      effectTime: effectTimeData,
      expireTime: expireTimeData,
      fixedPriceType,
    });
    return true;
  };

  useEffect(() => {
    log.addLog('byf-manage', 'visit');
    log.reportInit();
    handleOnGetStatus();
    handleOnGetAlipayInfo();
    handleOnGetArrearsAmount();
    handleOnGiveAdmit();
    handleOnGetDataIndicatorList();
  }, []);

  return (
    <div className={styles.manage}>
      <Loading visible={isLoading}>
        <Banner
          className={styles.deliveryBanner}
          itemClassName={styles.deliveryBannerItem}
          productType={PRODUCT_CODE}
          positionCode={HOME_BANNER_POSITION_CODE}
        />
        <NoticePanel data={tips} />
        <div className={styles['manage-content']} >
          <MenuHeader
            isOpenFixed={renderContent.isOpenFixed}
            status={renderContent.status}
            showSettings
            quitDialogLoading={isLoading}
            onQuit={handleOnApplyQuit}
            onShowQa={() => { setIsShowDetailQA(true); }}
            onShowProtocol={() => { setIsShowProtocol(true); }}
            giveType={renderContent?.giveType}
            partAdmit={partAdmit}
          />
          {
            renderContent.isOpenFixed ? (
              <FixedFeePanel
                isArrears={arrearsInfo.isArrears && arrearsInfo.showArrears}
                status={renderContent.status}
                fixedFeeAmount={renderContent.fixedFeeAmount}
                fixedPromotionFlag={renderContent.fixedPromotionFlag}
                fixedDiscountFeeAmount={renderContent.fixedDiscountFeeAmount}
                fixedPriceType={renderContent?.fixedPriceType}
                effectTime={renderContent.effectTime}
                expireTime={renderContent.expireTime}
                alipayLoginId={alipayLoginId}
                currFixedPriceExpireTime={renderContent?.currFixedPriceExpireTime}
                currFixedPriceExpireSoon={renderContent?.currFixedPriceExpireSoon}
                currFixedPriceRenewal={renderContent?.currFixedPriceRenewal}
                isFixedRenewalAdmit={renderContent?.isFixedRenewalAdmit}
                fixedRenewalData={fixedRenewalData}
                onRenew={async () => {
                  log.addLog('fixed-price-tip-show', 'click');
                  const isSuccess = await handleOnUnOpenFixedRenewlTrail();
                  isSuccess && setRenewalDialogVisible(true);
                }}
              />
            ) : (
              <BaseFeePanel
                isArrears={arrearsInfo.isArrears && arrearsInfo.showArrears}
                status={renderContent.status}
                baseFeeAmount={renderContent.baseFeeAmount}
                isFixedAdmit={renderContent.isFixedAdmit}
                fixedFeeAmount={renderContent.fixedFeeAmount}
                fixedPromotionFlag={renderContent.fixedPromotionFlag}
                fixedDiscountFeeAmount={renderContent.fixedDiscountFeeAmount}
                effectTime={renderContent.effectTime}
                expireTime={renderContent.expireTime}
                promotionFlag={renderContent.promotionFlag}
                discountFeeAmount={renderContent.discountFeeAmount}
                onFixedPrice={handleOnOpenFixedPrice}
                fixedPriceType={renderContent.fixedPriceType}
                alipayLoginId={alipayLoginId}
                promotionDetailList={renderContent.promotionDetailList}
                exemptionAmount={renderContent.exemptionAmount}
              />
            )
          }
          <DeliveryAnnouncement isOpenFixed={renderContent?.isOpenFixed} isFixedAdmit={renderContent?.isFixedAdmit} />
        </div>
        <ServiceData indicatorData={indicatorData} dataUpdateDeadline={dataUpdateDeadline} />
        <Drawer
          title="常见问题"
          placement="right"
          visible={isShowDetailQA}
          width={600}
          onClose={() => { setIsShowDetailQA(false); }}
        >
          <div style={{ padding: '0 22px' }}>
            <CommonQa isLogoOverflow noTitle />
          </div>
        </Drawer>
        <Drawer
          title="协议签署"
          placement="right"
          visible={isShowProtocol}
          width={600}
          onClose={() => { setIsShowProtocol(false); }}
        >
          <div style={{ padding: '0 22px' }}>
            <iframe
              width={550}
              height={710}
              style={{ border: 'none' }}
              src={PROTOCOL_URL}
            />
          </div>
        </Drawer>
        <Dialog
          v2
          title={`续签${PRICE_RENDER_CONFIG[fixedRenewalData?.fixedPriceType ?? 'default']?.title}`}
          visible={renewalDialogVisible}
          width={400}
          className={styles['renewal-dialog']}
          onOk={() => {
            log.addLog('fixed-renewal-dialog-sign-click', 'success');
            setRenewalDialogVisible(false);
            handleOnFixedRenewalApply();
          }}
          onCancel={() => {
            log.addLog('fixed-renewal-dialog-sign-cancel', 'success');
            setRenewalDialogVisible(false);
          }}
          onClose={() => {
            log.addLog('fixed-renewal-dialog-sign-close', 'success');
            setRenewalDialogVisible(false);
          }}
          okProps={{ children: '确认续签' }}
          cancelProps={{ children: '暂不续签' }}
        >
          <Message type="notice" size="medium" style={renewalDialogStyle}>
            {RENEWAL_DIALOG_TIPS}
          </Message>
          <div className={styles['renew-amount-wrapper']}>
            <div className={styles['renew-title']}>{PRICE_RENDER_CONFIG[fixedRenewalData?.fixedPriceType ?? 'default']?.title}</div>
            <div className={styles['renew-amount']}>
              {fixedRenewalData?.promotionFlag ? (
                <div className={styles['renew-amount-content']}>
                  <span>¥{money_US(fixedRenewalData?.discountFeeAmount)}</span>
                  <span className={styles['renew-unit']}> /单</span>
                  <span className={styles['renew-origin-content']}>¥{money_US(fixedRenewalData?.originalFeeAmount)} /单</span>
                  <BalloonContent
                    icon={<Icon type="help" style={{ color: '#999', marginRight: 4 }} />}
                    align="tl"
                    contentWidth={120}
                    contentChildren={'该价格为优惠前的价格'}
                  />
                </div>
              ) : (
                <div className={styles['renew-amount-content']}>
                  <span>¥{money_US(fixedRenewalData?.originalFeeAmount)}</span>
                  <span className={styles['renew-unit']}> /单</span>
                </div>
              )
              }
            </div>
            <div className={styles['renew-time']}>
              有效期：
              {fixedRenewalData?.effectTime ? dayjs(fixedRenewalData?.effectTime).format('YYYY-MM-DD') : '--'} ～
              {fixedRenewalData?.expireTime ? dayjs(fixedRenewalData?.expireTime).format('YYYY-MM-DD') : '--'}
            </div>
          </div>
        </Dialog>
      </Loading>
    </div>
  );
};
export default Manage;
