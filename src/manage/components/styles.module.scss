// PayInfo
.pay-info {
  display: flex;
  flex-direction: row;
  font-size: 14px;
  line-height: 30px;
  letter-spacing: 0;
  color: #666;
  .pay-normal-icon {
    margin-top: 10px;
    margin-left: 2px;
    width: 12px;
    height: 12px;
    background-image: url("https://img.alicdn.com/imgextra/i4/O1CN01WOM9TZ1b5f8zdsAry_!!6000000003414-2-tps-24-24.png");
    background-size: 12px 12px;
    background-repeat: no-repeat;
  }
  .pay-alipay-icon {
    margin-top: 8px;
    margin-right: 2px;
    width: 14px;
    height: 14px;
    background-image: url("https://img.alicdn.com/imgextra/i3/O1CN01F6taE01POeAfesm0x_!!6000000001831-2-tps-28-28.png");
    background-size: 14px 14px;
    background-repeat: no-repeat;
  }
}

// BaseFeePanel
.base-fee-panel {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  min-width: 100px;
  margin-top: 18px;

  .base-fee-panel-content {
    display: flex;
    flex-direction: row;

    .fee-daily {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      margin: auto 0;
      min-width: 300px;
      .fee-daily-base {
        display: flex;
        flex-direction: column;
        align-content: center;
        margin-right: 42px;
        font-size: 14px;
        font-weight: normal;
        line-height: 20px;
        color: #111;
        .fee-daily-base-title {
          display: flex;
          flex-direction: row;
          align-items: center;
          font-size: 14px;
          font-weight: 600;
          line-height: 14px;
          color: #111;
          .fee-daily-base-title-market {
            font-size: 12px;
            margin-left: 6px;
            color: #ff5000;
            padding: 3px 9px;
            line-height: 18px;
            width: auto;
            height: 24px;
            border-radius: 6px;
            background-color: #ffede5;
          }
        }
      }
      .fee-daily-half-content {
        display: flex;
        flex-direction: row;
        .fee-daily-half-content-main {
          .fee-daily-half-promotion {
            display: flex;
            flex-direction: row;
            .fee-daily-half-promotion-content {
              font-size: 14px;
              font-weight: 500;
              color: #111;
              span {
                line-height: 36px;
                font-family: "AlibabaFontMd";
                font-size: 30px;
              }
            }
          }
        }
        .fee-daily-half-promotion-info {
          display: flex;
          width: 284px;
          height: 60px;
          .fee-daily-half-promotion-info-title {
            font-size: 12px;
            color: #111;
          }
        }
      }
      .fee-daily-base-promotion {
        display: flex;
        align-items: flex-end;
        margin-top: 6px;
        .fee-daily-base-promotion-discount {
          font-size: 14px;
          font-weight: 500;
          color: #111;
          span {
            line-height: 36px;
            font-family: "AlibabaFontMd";
            font-size: 30px;
          }
        }
        .fee-daily-base-promotion-discount-origin {
          display: flex;
          flex-direction: row;
          color: #999;
          font-size: 14px;
          margin: 0 6px;
          text-decoration: line-through;
          text-decoration-color: rgba(153, 153, 153, 0.5);
        }
        .fee-daily-base-promotion-discount-tag {
          display: flex;
          flex-direction: column;
          justify-content: center;
          width: 94px;
          height: 24px;
          align-items: center;
          color: #ff8000;
          font-weight: normal;
          font-size: 14px;
          padding: 2px;
          border-radius: 6px;
          background: rgba(255, 128, 0, 0.06);

          .promotion-icon {
            margin-left: 2px;
            width: 14px;
            height: 14px;
            background-image: url("https://gw.alicdn.com/imgextra/i3/O1CN01btxTIO1LS4gN5DjCk_!!6000000001297-2-tps-24-24.png");
            background-size: 14px 14px;
            background-repeat: no-repeat;
          }
        }
      }
      .fee-daily-base-un-promotion {
        margin-top: 6px;
        font-size: 14px;
        font-weight: 500;
        line-height: 100%;
        letter-spacing: 0;
        color: #111;
        span {
          line-height: 36px;
          font-family: "AlibabaFontMd";
          font-size: 30px;
        }
      }
    }

    .fee-fixed {
      display: flex;
      flex-direction: column;
      margin-left: 32px;
      padding: 12px;
      min-width: 440px;
      height: 116px;
      border: 1px solid #e4e6ed;
      border-radius: 18px;
      .fee-fixed-head {
        display: flex;
        flex-direction: row;
        height: 24px;
        align-items: center;
        .fee-fixed-head-price {
          display: flex;
          flex-direction: row;
          margin-right: 9px;
          font-size: 16px;
          font-weight: 600;
          line-height: 24px;
          letter-spacing: 0;
          color: #111;
        }
      }
      .fee-fixed-content {
        display: flex;
        flex-direction: row;
        height: 34px;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
        font-weight: normal;
        line-height: 20px;
        margin: 4px 0;
        letter-spacing: 0;
        color: #ff8000;
        .fee-fixed-content-amount {
          font-size: 14px;
          font-weight: 400;
          color: #999;
          display: flex;
          flex-direction: row;
          align-items: baseline;
          .fee-fixed-content-amount-discount {
            font-family: "AlibabaFontMd";
            font-size: 24px;
            font-weight: 500;
            color: #ff8000;
          }
          .fee-fixed-content-amount-unit {
            font-size: 14px;
            font-weight: 400;
            color: #ff8000;
            margin-right: 4px;
          }
          .fee-fixed-content-amount-origin {
            font-size: 14px;
            font-weight: 400;
            color: #999;
            text-decoration: line-through;
            font-family: "AlibabaFontMd";
          }
          .fee-fixed-content-amount-info {
            font-size: 12px;
          }
        }
        :global(.next-btn.next-btn-normal) {
          border-color: #ff8000;
          background-color: #ff8000;
          color: #fff;
          &:hover {
            background-color: #ff9f00;
            border-color: #ff8000;
          }
        }
      }
      .fee-fixed-desc {
        display: flex;
        flex-direction: row;
        align-items: center;
        height: 20px;
        font-size: 12px;
        font-weight: normal;
        line-height: 20px;
        margin: 4px 0;
        color: #111;
        span {
          font-size: 12px;
          color: #666;
          margin-right: 4px;
        }
        .fee-fixed-desc-rule {
          margin-top: 2px;
          margin-left: 9px;
          font-size: 12px;
          font-weight: normal;
          line-height: 18px;
          text-align: center;
          letter-spacing: 0;
        }
      }
    }
  }
}

// FixedFeePanel
.fixed {
  display: flex;
  flex-direction: row;
  margin-top: 18px;
  .fixed-fee {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    width: auto;
    // height: 88px;
    .fixed-fee-title {
      font-size: 14px;
      font-weight: 600;
      line-height: 20px;
      margin-bottom: 8px;
      color: #111;
    }
    .fixed-fee-content {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: flex-end;
      height: 34px;
      .fixed-fee-content-title-info {
        margin-top: 4px;
        line-height: 30px;
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
        color: #111;
      }
      .fixed-fee-content-title-amount {
        margin-left: 4px;
        font-family: "AlibabaFontMd";
        font-size: 30px;
        font-weight: 500;
        line-height: 30px;
        letter-spacing: 0;
        color: #111;
      }
      .fixed-fee-content-title-unit {
        font-size: 14px;
        font-weight: normal;
        line-height: 20px;
        margin-right: 6px;
        color: #111;
      }
      .fixed-fee-content-title-origin {
        font-size: 14px;
        font-weight: normal;
        text-decoration: line-through;
        line-height: 20px;
        margin-right: 6px;
        color: #999;
      }
    }
    .fixed-fee-info {
      display: flex;
      flex-direction: row;
      margin-top: 4px;
      .fixed-fee-content-stage {
        display: flex;
        justify-content: center;
        font-size: 14px;
        font-weight: normal;
        line-height: 30px;
        letter-spacing: 0;
        color: #666;
        margin-right: 10px;
      }
    }
  }
  .fixed-fee-arrears {
    margin-top: 18px;
  }
}

// FixedFeeRenew
.fixed-fee-renew {
  font-family: PingFang SC;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-radius: 8px;
  box-sizing: border-box;
  margin: 18px 0;

  .fixed-fee-renew-title {
    display: flex;
    align-items: center;
    margin-bottom: 4px;

    .fixed-fee-renew-text {
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;
      color: #111;
      margin-right: 8px;
    }

    .fixed-fee-renew-tip {
      display: flex;
      align-items: center;
      color: #666;
      border-radius: 6px;
      font-size: 12px;
      padding: 4px 8px;

      img {
        width: 16px;
        height: 16px;
        margin-right: 4px;
      }

      span {
        font-size: 12px;
      }
    }
  }

  .fixed-fee-renew-content {
    font-size: 12px;

    .fixed-fee-renew-content-text {
      line-height: 18px;
      color: #111;

      span {
        color: #ff5000;
      }
    }

    .fixed-fee-renew-content-amount {
      display: flex;
      align-items: center;
      color: #111;
      line-height: 22px;
      gap: 4px;

      .fixed-fee-renew-amount {
        font-size: 16px;
        font-weight: 500;
      }
      .fixed-fee-renew-unit {
        font-weight: 400;
      }
      .fixed-fee-renew-origin {
        font-size: 12px;
        font-weight: 400;
        color: #999;
        text-decoration: line-through;
      }
    }
  }

  .fixed-fee-renew-btn {
    border-radius: 4px;
  }
}

// ServiceData
.service-data {
  display: flex;
  flex-direction: column;
  width: 100%;

  .service-data-title {
    font-size: 14px;
    font-weight: 600;
    line-height: 24px;
    color: #111;
    span {
      margin-left: 6px;
      font-weight: normal;
      font-size: 12px;
      line-height: 24px;
      color: #999;
    }
  }
  .service-data-panel {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    margin-top: 12px;
    gap: 12px;
    .service-data-panel-info {
      width: 222px;
      height: 89px;
      padding: 18px;
      background: #f7f8fa;
      border-radius: 12px;

      .service-data-panel-info-key {
        display: flex;
        flex-direction: row;
        font-size: 14px;
        font-weight: normal;
        line-height: 20px;
        letter-spacing: 0;
        color: #111;
        font-family: "AlibabaFontMd";
        margin-bottom: 10px;
        span {
          font-size: 12px;
          color: #999;
        }

        .service-data-panel-info-key-tag {
          width: 44px;
          height: 18px;
          display: flex;
          flex-direction: row;
          justify-content: center;
          background-color: rgba(61, 94, 255, 0.06);
          font-size: 12px;
          font-weight: normal;
          line-height: 18px;
          color: #3d5eff;
          border-radius: 6px;
        }
      }
      .service-data-panel-info-val {
        font-family: "AlibabaFontMd";
        font-size: 24px;
        font-weight: 500;
        line-height: 18px;
        letter-spacing: 0;
        color: #111;
        span {
          margin-left: 2px;
          font-size: 16px;
          font-weight: normal;
        }
      }
    }
  }
}

// ArrearsPanel
.arrears {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;

  width: 318px;
  height: 106px;
  border-radius: 12px;
  padding: 12px;
  background: rgba(255, 0, 0, 0.06);
  color: #111;
  .arrears-tips {
    font-size: 14px;
    font-weight: normal;
    line-height: 32px;
    color: #f00;
  }
  .arrears-title {
    font-size: 14px;
    font-weight: normal;
    line-height: 26px;
  }
  .arrears-amount {
    font-family: "AlibabaFontMd";
    font-size: 18px;
    font-weight: 500;
    line-height: 18px;
  }
}
