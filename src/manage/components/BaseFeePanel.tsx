import React from 'react';
import styles from './styles.module.scss';
import ServiceFeeArrears from '@/components/ServiceFeeArrears';
import { money_US } from '@ali/iec-dtao-utils/esm/common/number';
import { Balloon, Button, Icon } from '@alifd/next';
import { PRICE_RENDER_CONFIG, UNQUIET_STATUS } from '@/constant';
import dayjs from 'dayjs';
import PayInfo from './PayInfo';
import { includes } from 'lodash-es';
import CommonTag from '@/components/CommonTag';
import BalloonContent from '@/components/BalloonContent';
import PromotionPopup from '@/components/PromotionPopup';

const renderPromotionContent = ({
  promotionFlag,
  discountFeeAmount,
  baseFeeAmount,
  // marketTitle,
  promotionDetailList,
  exemptionAmount,
}) => {
  if (promotionFlag) {
    return (
      <>
        <div className={styles['fee-daily-base-title']}>
          预估服务费
          <BalloonContent
            icon={<Icon type="help" style={{ color: '#999' }} />}
            align="t"
            contentChildren={'当前服务费为预估服务费，费用按单收取，我们将根据您店铺交易订单及退换货情况每日动态调整服务费，最终服务费以出单为准'}
          />
          <PromotionPopup
            promotionDetailList={promotionDetailList}
            exemptionAmount={exemptionAmount}
            discountFeeAmount={discountFeeAmount}
            originalFeeAmount={baseFeeAmount}
          />
        </div>
        <div className={styles['fee-daily-base-promotion']}>
          <div className={styles['fee-daily-base-promotion-discount']}>
            <span>¥{money_US(discountFeeAmount) || '--'}</span> /单
          </div>
          <div className={styles['fee-daily-base-promotion-discount-origin']}>
            ¥{money_US(baseFeeAmount) || '--'}/单
            <BalloonContent
              icon={<Icon type="help" />}
              align="tl"
              contentWidth={120}
              contentChildren={'该价格为优惠前的价格'}
            />
          </div>
        </div>
      </>
    );
  }
  return (
    <>
      <div className={styles['fee-daily-base-title']}>
        预估服务费
        <BalloonContent
          icon={<Icon type="help" style={{ color: '#999' }} />}
          align="t"
          contentChildren={'当前服务费为预估服务费，费用按单收取，我们将根据您店铺交易订单及退换货情况每日动态调整服务费，最终服务费以出单为准'}
        />
      </div>
      <div className={styles['fee-daily-base-un-promotion']}>
        <span>¥{money_US(baseFeeAmount) || '--'}</span> /单
      </div>
    </>
  );
};

const BaseFeePanel = ({
  status,
  baseFeeAmount,
  fixedFeeAmount,
  fixedPromotionFlag,
  fixedDiscountFeeAmount,
  isFixedAdmit,
  effectTime,
  expireTime,
  isArrears,
  onFixedPrice,
  fixedPriceType,
  alipayLoginId,
  discountFeeAmount,
  promotionFlag,
  // marketTitle,
  promotionDetailList,
  exemptionAmount,
}) => {
  // 日常版命中退出中、冻结中
  if (includes(UNQUIET_STATUS, status)) {
    return (
      <div className={styles['base-fee-panel']}>
        <PayInfo alipayLoginId={alipayLoginId} />
      </div>
    );
  }

  return (
    <div className={styles['base-fee-panel']}>
      <div className={styles['base-fee-panel-content']}>
        <div className={styles['fee-daily']}>
          <div className={styles['fee-daily-base']}>
            {renderPromotionContent({
              promotionFlag,
              discountFeeAmount,
              baseFeeAmount,
              // marketTitle,
              promotionDetailList,
              exemptionAmount,
            })}
          </div>
          <PayInfo alipayLoginId={alipayLoginId} />
        </div>
        <ServiceFeeArrears isArrears={isArrears} logKey="base-fee-arrears-btn" />
        {
          isFixedAdmit && (
            <div className={styles['fee-fixed']}>
              <div className={styles['fee-fixed-head']}>
                <div className={styles['fee-fixed-head-price']}>{PRICE_RENDER_CONFIG[fixedPriceType]?.title}</div>
                <CommonTag bgcolor={'rgba(255, 128, 0, 0.06)'} color={'#FF8000'} text={PRICE_RENDER_CONFIG[fixedPriceType]?.desc} />
              </div>
              <div className={styles['fee-fixed-content']}>
                {
                  fixedPromotionFlag ? (
                    <div className={styles['fee-fixed-content-amount']}>
                      <span className={styles['fee-fixed-content-amount-discount']}>¥{money_US(fixedDiscountFeeAmount) || '--'}</span>
                      <span className={styles['fee-fixed-content-amount-unit']}>/单</span>
                      <span className={styles['fee-fixed-content-amount-origin']}>¥{money_US(fixedFeeAmount) || '--'} /单</span>
                      <span className={styles['fee-fixed-content-amount-info']}>
                        <BalloonContent
                          icon={<Icon type="help" style={{ color: '#999', marginRight: 4 }} />}
                          align="tl"
                          contentWidth={120}
                          contentChildren={'该价格为优惠前的价格'}
                        />
                      </span>
                    </div>
                  ) : (
                    <div className={styles['fee-fixed-content-amount']}>
                      <span className={styles['fee-fixed-content-amount-discount']}>¥{money_US(fixedFeeAmount) || '--'}</span> /单
                    </div>
                  )
                }
                <Button disabled={!fixedFeeAmount} onClick={() => onFixedPrice()}>立即开通</Button>
              </div>
              <div className={styles['fee-fixed-desc']}>
                <span>有效期 {effectTime ? dayjs(effectTime).format('YYYY-MM-DD') : '-'} ~ {expireTime ? dayjs(expireTime).format('YYYY-MM-DD') : '-'}</span>
                承诺在有效期内不中途退出
                <Balloon
                  v2
                  trigger={<div className={styles['fee-fixed-desc-rule']}>活动规则 &gt;</div>}
                  align="rt"
                  triggerType="hover"
                  title={`${PRICE_RENDER_CONFIG[fixedPriceType]?.title}活动规则`}
                >
                  <div style={{ lineHeight: '18px', width: 300 }}>
                    <div>该活动是限时邀请制，只针对部分符合准入条件的商家开放，商家不能主动申请参与。</div>
                    <div>活动规则如下： </div>
                    <div>1. 商家需要约定一定周期的服务在约时长，即可享一口价的优惠。在这个约定的时间内，服务费价格将保持不变。</div>
                    <div>2. 签约后，在这个约定的时间内，商家不能中途退出此服务。</div>
                  </div>
                </Balloon>
              </div>
            </div>
          )
        }
      </div>
    </div>
  );
};
export default BaseFeePanel;
