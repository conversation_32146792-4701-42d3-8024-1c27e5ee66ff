/**
* @file PriceCardItem
* @date 2025-07-01
* <AUTHOR>
*/
import React from 'react';
import styles from './index.module.scss';
import PromotionPopup from '../PromotionPopup';
import { money_US } from '@ali/iec-dtao-utils/esm/common/number';
import BalloonContent from '../BalloonContent';
import { Icon } from '@alifd/next';
import dayjs from 'dayjs';

const PriceCardItem = ({ item, selectedPriceType }) => {
  const priceType = item?.priceType;

  const getClassName = (name) => {
    try {
      const str = `
      ${styles[name]} 
      ${styles[`${priceType}${selectedPriceType === priceType ? '-selected' : ''}`]}
      `;
      return str;
    } catch (error) {
      return styles[name];
    }
  };

  return (
    <div className={getClassName('item-content')} >
      {
        item?.priceType !== 'DAILY' && selectedPriceType === item?.priceType && (
          <div className={styles['item-tag']}>
            <span>承诺有效期内不退出</span>
          </div>
        )
      }
      <>
        <div className={styles['item-title']}>{item.title}</div>
        <div className={styles['item-price']}>
          <PromotionPopup
            promotionDetailList={item?.promotionDetailList}
            exemptionAmount={item?.exemptionAmount}
            discountFeeAmount={item?.discountFeeAmount}
            originalFeeAmount={item?.originalFeeAmount}
          />
          <div className={styles.baseTrailAmount}>
            {
              item?.promotionFlag ? (
                <div className={styles['amount-content-item']}>
                  <span className={getClassName('highlight-amount')}>{money_US(item.discountFeeAmount) || '--'}</span>
                  <span className={getClassName('highlight-unit')}>元/单</span>
                  <span className={styles['base-amount']}>{money_US(item?.originalFeeAmount) || '--'} 元/单</span>
                  <BalloonContent
                    icon={<Icon type="help" size={12} style={{ marginBottom: '3px' }} />}
                    align="tl"
                    contentWidth={120}
                    contentChildren={'该价格为优惠前的价格'}
                  />
                </div>
              ) : (
                <div className={styles['amount-content-item']}>
                  <span className={getClassName('highlight-amount')}>{money_US(item.originalFeeAmount) || '--'}</span>
                  <span className={getClassName('highlight-unit')}>元/单</span>
                </div>
              )
            }
          </div>
        </div>
        <div className={styles['item-rule']}>
          {
            item?.priceType === 'DAILY' ? (
              <div className={styles['item-rule-desc']}>
                服务费将根据店铺交易订单退换货等情况动态调整，最终以实际出单收费为准
              </div>
            ) : (
              <div className={styles['item-rule-period']}>
                <div>有效期 {`${item?.effectTime ? dayjs(item?.effectTime).format('YYYY.MM.DD') : '--'}～${item?.expireTime ? dayjs(item?.expireTime).format('YYYY.MM.DD') : '--'}`}</div>
                <BalloonContent
                  icon={<span>活动规则<Icon type="arrow-right" size={12} style={{ color: '#666' }} /></span>}
                  align="tl"
                  contentWidth={300}
                  contentChildren={
                    <div className={styles['balloon-panel-content']}>
                      <div className={styles['balloon-panel-title']}>专属一口价活动规则</div>
                      <div className={styles['balloon-panel-desc']}>
                        该活动是限时邀请制，只针对部分符合准入条件的商家开放，商家不能主动申请参与。<br />
                        活动规则如下：<br />
                        1. 商家需要约定一定周期的服务在约时长，即可享受专属一口价的优惠。在这个约定的时间内，服务费价格将保持不变。<br />
                        2. 签约后，在这个约定的时间内，商家不能中途退出此服务。服务到期后将恢复日常动态定价。
                      </div>
                    </div>
                  }
                />
              </div>)
          }
        </div>
      </>
      <div className={getClassName('item-footer')}>
        {item?.desc}
      </div>
    </div >
  );
};

export default PriceCardItem;
